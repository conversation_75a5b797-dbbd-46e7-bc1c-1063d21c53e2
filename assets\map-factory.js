import { LocationPage } from '@theme/location-page';
import { HomePageMap } from '@theme/home-page-map';

/**
 * Factory class to create the appropriate map instance based on page type
 */
export class MapFactory {
  /**
   * Create the appropriate map instance based on the current page template
   * @param {HTMLElement} element - The map container element
   * @returns {LocationPage|HomePageMap} - The appropriate map instance
   */
  static createMap(element) {
    const pageType = MapFactory.detectPageType();
    
    switch (pageType) {
      case 'location':
        return new LocationPage(element);
      case 'home':
        return new HomePageMap(element);
      default:
        // Default to location page for unknown page types
        console.warn(`Unknown page type: ${pageType}. Defaulting to LocationPage.`);
        return new LocationPage(element);
    }
  }

  /**
   * Detect the current page type based on various indicators
   * @returns {string} - The detected page type ('location', 'home', or 'unknown')
   */
  static detectPageType() {
    // Method 1: Check Shopify template variable if available
    if (typeof window.Shopify !== 'undefined' && window.Shopify.template) {
      const template = window.Shopify.template;
      if (template === 'page.locations') return 'location';
      if (template === 'index') return 'home';
    }

    // Method 2: Check for template meta tag
    const templateMeta = document.querySelector('meta[name="template"]');
    if (templateMeta) {
      const template = templateMeta.getAttribute('content');
      if (template === 'page.locations') return 'location';
      if (template === 'index') return 'home';
    }

    // Method 3: Check URL path
    const path = window.location.pathname;
    if (path.includes('/pages/locations') || path.includes('/locations')) {
      return 'location';
    }
    if (path === '/' || path === '/index' || path === '') {
      return 'home';
    }

    // Method 4: Check for specific page indicators
    // Check for Apply Now button (indicates location page)
    if (document.getElementById('applyAllFilters')) {
      return 'location';
    }

    // Check for location page specific elements
    if (document.querySelector('.filter-wrapper') && 
        document.querySelector('.sidebar-location')) {
      return 'location';
    }

    // Method 5: Check body classes
    const bodyClasses = document.body.className;
    if (bodyClasses.includes('template-page') && bodyClasses.includes('locations')) {
      return 'location';
    }
    if (bodyClasses.includes('template-index')) {
      return 'home';
    }

    // Method 6: Check for data attributes on the map element
    const mapElement = document.querySelector('map-location');
    if (mapElement) {
      const pageType = mapElement.dataset.pageType;
      if (pageType) return pageType;
    }

    return 'unknown';
  }

  /**
   * Get configuration for the detected page type
   * @param {string} pageType - The page type
   * @returns {Object} - Configuration object for the page type
   */
  static getPageConfig(pageType) {
    const configs = {
      location: {
        enableSidebar: true,
        enableFilters: true,
        enableDelayedFiltering: true,
        enableQueryParams: true,
        enableFullFeatures: true,
        defaultZoom: 10,
        showApplyButton: true
      },
      home: {
        enableSidebar: false,
        enableFilters: false,
        enableDelayedFiltering: false,
        enableQueryParams: false,
        enableFullFeatures: false,
        defaultZoom: 5,
        showApplyButton: false
      }
    };

    return configs[pageType] || configs.location;
  }

  /**
   * Initialize the map based on the current page context
   * @param {HTMLElement} element - The map container element
   * @returns {Promise<LocationPage|HomePageMap>} - The initialized map instance
   */
  static async initializeMap(element) {
    const pageType = MapFactory.detectPageType();
    const config = MapFactory.getPageConfig(pageType);
    
    // Apply configuration to the element if needed
    MapFactory.applyConfigToElement(element, config);
    
    // Create and initialize the appropriate map instance
    const mapInstance = MapFactory.createMap(element);
    
    // Call the connectedCallback to initialize the map
    await mapInstance.connectedCallback();
    
    return mapInstance;
  }

  /**
   * Apply configuration to the map element
   * @param {HTMLElement} element - The map container element
   * @param {Object} config - The configuration object
   */
  static applyConfigToElement(element, config) {
    // Set data attributes based on configuration
    if (config.enableSidebar !== undefined) {
      element.dataset.enableSidebar = config.enableSidebar.toString();
    }
    
    if (config.enableQueryParams !== undefined) {
      element.dataset.enableUpdateQuearyParams = config.enableQueryParams.toString();
    }
    
    // Add page type for future reference
    element.dataset.pageType = MapFactory.detectPageType();
  }
}

/**
 * Custom element that uses the factory to create the appropriate map instance
 */
class MapLocation extends HTMLElement {
  constructor() {
    super();
    this.mapInstance = null;
  }

  async connectedCallback() {
    try {
      // Use the factory to create and initialize the appropriate map instance
      this.mapInstance = await MapFactory.initializeMap(this);
    } catch (error) {
      console.error('Error initializing map:', error);
    }
  }

  disconnectedCallback() {
    // Clean up the map instance
    if (this.mapInstance && typeof this.mapInstance.disconnectedCallback === 'function') {
      this.mapInstance.disconnectedCallback();
    }
  }

  /**
   * Get the current map instance
   * @returns {LocationPage|HomePageMap|null} - The current map instance
   */
  getMapInstance() {
    return this.mapInstance;
  }

  /**
   * Get the page type
   * @returns {string} - The detected page type
   */
  getPageType() {
    return MapFactory.detectPageType();
  }
}

// Define the custom element
customElements.define('map-location', MapLocation);

export { MapLocation };
