import { MapBase } from '@theme/map-base';
import { GoogleMapsLoader } from '@theme/locations-services';
import { LocationApiService } from '@theme/locations-services';

/**
 * Location page specific implementation with full filtering capabilities
 */
export class LocationPage extends MapBase {
  constructor(element) {
    super(element);
    this.initializeLocationPageProperties();
  }

  initializeLocationPageProperties() {
    // Check if "Apply Now" button exists to determine if we should use delayed mobile filtering
    this.hasApplyButton = !!document.getElementById('applyAllFilters');
    this.shouldUseDelayedMobileFiltering = this.isMobile && this.hasApplyButton;

    if (this.isEnabledQueryParams) {
      this.selectedFilterCount = 0;
      this.nearByMeParams = 'near_by_me';
      this.businessTypeParams = 'business_type';
      this.radiusParams = 'radius';
      this.searchParams = 'search';
      this.latParams = 'lat';
      this.lngParams = 'lng';
    }

    this.isCenterChangeRestricted = false;
    this.locakedCenterPoint = null;
  }

  /**
   * Lifecycle method that runs when the component is added to the DOM.
   */
  async connectedCallback() {
    this.setFilterLoading(true);
    this.detectFilteringBehavior();

    await this.initializeMapLocation();
    this.bindUIEvents();

    if (this.isEnabledQueryParams) {
      this.parseSearchParams();
    }

    // Listen for custom 'filterDropdown:change' event
    document.addEventListener('filterDropdown:change', this.onFilterDropdownChange.bind(this));
  }

  /**
   * Initialize the map location for location page
   */
  async initializeMapLocation() {
    const googleApiKey = this.element.dataset.googleApiKey;
    const locationsApi = this.element.dataset.locationsApi;

    if (!googleApiKey || !locationsApi) {
      console.error('Missing required data attributes: googleApiKey or locationsApi');
      return;
    }

    try {
      this.mapLoader.showLoader();
      await this.checkGeolocationPermission();

      // Load Google Maps API with the required libraries
      await GoogleMapsLoader.loadGoogleMapsAPI({
        key: googleApiKey,
        libraries: 'maps,geometry,marker',
        markerClusterer: true,
      });

      // Fetch location data from the API
      const locations = await LocationApiService.fetchLocations(locationsApi);
      if (!locations) {
        throw new Error('Unable to fetch locations data');
      }

      const allowedCountries =
        this.element.dataset.filterCountries?.split(',').map((country) => country.trim().toLowerCase()) ?? [];

      this.allLocations = Array.isArray(locations)
        ? locations.filter((location) => {
            const isInAllowedCountry =
              allowedCountries.length === 0 || allowedCountries.includes(location.country?.trim()?.toLowerCase());
            return isInAllowedCountry;
          })
        : [];

      // Initialize the map inside the specified container
      await this.initializeMap(this.element.querySelector('.map-container'));

      // Manually trigger the initial map update
      if (this.map && this.map.getBounds()) {
        this.handleMapChangeEvent(this.map.getBounds());
      }

      // Handle initial geolocation after map is initialized
      if (this.initialGeolocationPermission?.state === 'granted' && this.initialGeolocationPermission.enableNearMe) {
        navigator.geolocation.getCurrentPosition(
          (position) => {
            this.processGeolocation(position);
            this.toggleNearMeButton(true);
          },
          (error) => {
            this.displayGeolocationError(error);
            this.loadDefaultUSALocations();
          }
        );
      } else {
        this.loadDefaultUSALocations();
      }
    } catch (error) {
      console.error('Error initializing map location:', error);
    } finally {
      this.mapLoader.hideLoader();
      this.setFilterLoading(false);
    }
  }

  /**
   * Bind UI events for location page
   */
  bindUIEvents() {
    // Bind search input event if enabled
    if (this.element.dataset.enableSearchInput === 'true') {
      this.bindSearchInputEvent();
    }

    // Bind near me event if enabled
    if (this.element.dataset.enableNearMe === 'true') {
      this.nearMeButtonElem = this.element.querySelector('.near-me-button');
      if (this.nearMeButtonElem) {
        this.bindNearMeEvent();
      }
    }

    // Check if all filters are enabled and bind their events
    if (
      ['enableRadiusFilter', 'enableBusinessTypeFilter', 'enableSearchInput', 'enableNearMe'].every(
        (key) => this.element.dataset[key] === 'true'
      )
    ) {
      this.bindRadiusFilterEvent();
      this.bindBusinessTypeFilterEvent();
      this.bindApplyAllFiltersEvent();
      this.resetAllFilters();
      this.handleClose();
    }
  }

  /**
   * Determine if delayed filtering should be used (location page specific)
   */
  shouldUseDelayedFiltering() {
    return this.shouldUseDelayedMobileFiltering;
  }

  /**
   * Check if there are active filters (location page specific)
   */
  hasActiveFilters() {
    return (
      this.selectedNearByMe ||
      this.selectedInputSearch ||
      this.selectedRadius !== this.defaultRadius ||
      this.selectedBusinessType !== this.defaultBusinessType
    );
  }

  /**
   * Determine if sidebar should be displayed (location page specific)
   */
  shouldDisplaySidebar() {
    return this.element.dataset.enableSidebar === 'true';
  }

  /**
   * Detects if Apply Now button exists and sets filtering behavior accordingly.
   */
  detectFilteringBehavior() {
    this.hasApplyButton = !!document.getElementById('applyAllFilters');
    this.shouldUseDelayedMobileFiltering = this.isMobile && this.hasApplyButton;
  }

  /**
   * Handles the map idle event for location page
   */
  handleMapChangeEvent(bounds, forceUpdate = false) {
    // On mobile with Apply button, only apply filters when explicitly forced
    if (this.shouldUseDelayedFiltering() && !forceUpdate && this.hasActiveFilters()) {
      return; // Skip filter application on mobile location page unless forced
    }

    // Reset the radius to default if the map center is outside the locked center point
    if (this.locakedCenterPoint && bounds && !bounds.contains(this.locakedCenterPoint)) {
      this.selectedRadius = this.defaultRadius;
    }

    const filteredLocationsWithinBounds = this.filterLocationsWithinBounds(bounds);

    // Update the selected location coordinates only if the center is not restricted
    if (!this.isCenterChangeRestricted) {
      this.selectedLocationCoordinates = {
        lat: this.map.getCenter().lat(),
        lng: this.map.getCenter().lng(),
      };
    }

    // Update URL query parameters if enabled
    if (this.isEnabledQueryParams && (!this.enableSearchInput || !this.selectedNearByMe)) {
      this.updateURLQueryParams(
        this.selectedNearByMe,
        this.selectedInputSearch,
        this.selectedLocationCoordinates.lat,
        this.selectedLocationCoordinates.lng,
        this.selectedRadius,
        this.selectedBusinessType
      );
    }

    // Filter locations based on the selected criteria
    const newFilteredLocations = this.filterLocations(
      filteredLocationsWithinBounds,
      this.selectedLocationCoordinates.lat,
      this.selectedLocationCoordinates.lng,
      this.selectedRadius,
      this.selectedBusinessType
    );

    // Only load locations if they have actually changed
    if (this.locationsHaveChanged(newFilteredLocations)) {
      this.previousFilteredLocations = [...newFilteredLocations];
      this.filteredLocations = newFilteredLocations;
      this.loadLocations(this.filteredLocations);
    }
  }

  // Filters locations within the current map bounds
  filterLocationsWithinBounds(bounds) {
    if (!bounds) return [];

    const filtered = this.allLocations.filter((location) => {
      const locationLatLng = new google.maps.LatLng(parseFloat(location.latitude), parseFloat(location.longitude));
      return bounds.contains(locationLatLng);
    });

    return filtered;
  }

  /**
   * Check geolocation permission for location page
   */
  async checkGeolocationPermission() {
    if (!navigator.geolocation) {
      return;
    }

    try {
      const permissionStatus = await navigator.permissions.query({ name: 'geolocation' });

      // Store the permission result to handle after map initialization
      this.initialGeolocationPermission = {
        state: permissionStatus.state,
        enableNearMe: this.element.dataset.enableNearMe === 'true',
      };

      // Listen for permission changes
      permissionStatus.addEventListener('change', () => {
        if (permissionStatus.state === 'granted') {
          this.handleNearMeClick();
        } else {
          this.loadDefaultUSALocations();
        }
      });
    } catch (error) {
      this.loadDefaultUSALocations();
    }
  }

  loadDefaultUSALocations() {
    this.selectedLocationCoordinates = this.defaultLocationCoordinates;
    this.isCenterChangeRestricted = false;
    this.selectedNearByMe = null;
    this.selectedRadius = this.defaultRadius;

    // Center map on default USA coordinates only if map is initialized
    if (this.map) {
      this.centerMapUpdateZoomLevel(this.defaultLocationCoordinates, this.zoomLevel);
    }
  }

  /**
   * Check if locations have changed
   */
  locationsHaveChanged(newLocations) {
    if (!this.previousFilteredLocations) return true;
    if (this.previousFilteredLocations.length !== newLocations.length) return true;

    // Compare location IDs
    const previousIds = new Set(this.previousFilteredLocations.map((loc) => loc.userId));
    const newIds = new Set(newLocations.map((loc) => loc.userId));

    if (previousIds.size !== newIds.size) return true;

    for (let id of newIds) {
      if (!previousIds.has(id)) return true;
    }

    return false;
  }

  /**
   * Filter locations based on criteria
   */
  filterLocations(locations, lat, lng, radius, businessType) {
    radius = radius ? radius * 1609.34 : Infinity;

    let filteredLocations = locations.filter((location) => {
      try {
        const distance = this.calculateDistance(
          lat,
          lng,
          parseFloat(location.latitude),
          parseFloat(location.longitude)
        );

        return distance <= radius;
      } catch (error) {
        return false;
      }
    });

    // Filter by business type if specified
    if (businessType) {
      filteredLocations = filteredLocations.filter((location) => {
        return location.businessType === businessType;
      });
    }

    // Sort locations with Health Pass Partners first
    return this.sortLocationsByPriority(filteredLocations);
  }

  /**
   * Sort locations by priority (Health Pass Partners first)
   */
  sortLocationsByPriority(locations) {
    const healthPassLocations = locations.filter((location) =>
      this.specialOfferingHealthPass.some((offering) =>
        location.specialOffering?.toLowerCase().includes(offering.toLowerCase())
      )
    );

    const nonHealthPassLocations = locations.filter(
      (location) =>
        !this.specialOfferingHealthPass.some((offering) =>
          location.specialOffering?.toLowerCase().includes(offering.toLowerCase())
        )
    );

    // Sorting function to arrange locations by distance
    const sortByDistance = (locations) =>
      locations.sort((a, b) => {
        const distanceToA = this.calculateDistance(
          this.selectedLocationCoordinates.lat,
          this.selectedLocationCoordinates.lng,
          parseFloat(a.latitude),
          parseFloat(a.longitude)
        );

        const distanceToB = this.calculateDistance(
          this.selectedLocationCoordinates.lat,
          this.selectedLocationCoordinates.lng,
          parseFloat(b.latitude),
          parseFloat(b.longitude)
        );

        return distanceToA - distanceToB;
      });

    return [...sortByDistance(healthPassLocations), ...sortByDistance(nonHealthPassLocations)];
  }

  /**
   * Load locations on map and sidebar
   */
  loadLocations(locations) {
    const selectedLocationId = this.getSelectedLocationId();
    const selectedMarkerPosition = this.getSelectedMarkerPosition();
    const selectedLocation = this.findLocationById(locations, selectedLocationId);

    // Move the selected location to the top of the list
    locations = this.prioritizeSelectedLocation(locations, selectedLocation);

    // Update sidebar list if enabled
    if (this.shouldDisplaySidebar()) {
      this.displayLocations(locations, selectedLocationId);
      this.scrollToSelectedLocation(selectedLocationId);
    }

    const markers = this.placeMarkers(locations);
    this.initMarkerClusterer(markers);

    // Re-select previously selected marker/location if applicable
    this.restoreSelectedMarker(selectedLocation, selectedMarkerPosition, locations);
  }

  // Placeholder methods that would need full implementation
  getSelectedLocationId() {
    return null;
  }
  getSelectedMarkerPosition() {
    return null;
  }
  findLocationById(_locations, _id) {
    return null;
  }
  prioritizeSelectedLocation(locations, _selectedLocation) {
    return locations;
  }
  displayLocations(_locations, _selectedLocationId) {
    /* Implementation needed */
  }
  scrollToSelectedLocation(_selectedLocationId) {
    /* Implementation needed */
  }
  restoreSelectedMarker(_selectedLocation, _selectedMarkerPosition, _locations) {
    /* Implementation needed */
  }
  bindSearchInputEvent() {
    /* Implementation needed */
  }
  bindNearMeEvent() {
    /* Implementation needed */
  }
  bindRadiusFilterEvent() {
    /* Implementation needed */
  }
  bindBusinessTypeFilterEvent() {
    /* Implementation needed */
  }
  bindApplyAllFiltersEvent() {
    /* Implementation needed */
  }
  resetAllFilters() {
    /* Implementation needed */
  }
  handleClose() {
    /* Implementation needed */
  }
  onFilterDropdownChange() {
    /* Implementation needed */
  }
  parseSearchParams() {
    /* Implementation needed */
  }
  updateURLQueryParams() {
    /* Implementation needed */
  }
  processGeolocation(_position) {
    /* Implementation needed */
  }
  displayGeolocationError(_error) {
    /* Implementation needed */
  }
  toggleNearMeButton(_state) {
    /* Implementation needed */
  }
}
