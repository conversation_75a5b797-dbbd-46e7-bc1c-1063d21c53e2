import { MapBase } from '@theme/map-base';
import { GoogleMapsLoader } from '@theme/locations-services';
import { LocationApiService } from '@theme/locations-services';

/**
 * Home page specific implementation with simplified functionality
 */
export class HomePageMap extends MapBase {
  constructor(element) {
    super(element);
    this.initializeHomePageProperties();
  }

  initializeHomePageProperties() {
    // Home page doesn't use delayed filtering
    this.hasApplyButton = false;
    this.shouldUseDelayedMobileFiltering = false;
    
    // Simplified properties for home page
    this.isCenterChangeRestricted = false;
    this.locakedCenterPoint = null;
  }

  /**
   * Lifecycle method that runs when the component is added to the DOM.
   */
  async connectedCallback() {
    this.setFilterLoading(true);
    await this.initializeMapLocation();
    this.bindUIEvents();
  }

  /**
   * Initialize the map location for home page
   */
  async initializeMapLocation() {
    const googleApiKey = this.element.dataset.googleApiKey;
    const locationsApi = this.element.dataset.locationsApi;

    if (!googleApiKey || !locationsApi) {
      console.error('Missing required data attributes: googleApiKey or locationsApi');
      return;
    }

    try {
      this.mapLoader.showLoader();

      // Load Google Maps API with the required libraries
      await GoogleMapsLoader.loadGoogleMapsAPI({
        key: googleApiKey,
        libraries: 'maps,geometry,marker',
        markerClusterer: true,
      });

      // Fetch location data from the API
      const locations = await LocationApiService.fetchLocations(locationsApi);
      if (!locations) {
        throw new Error('Unable to fetch locations data');
      }

      const allowedCountries =
        this.element.dataset.filterCountries?.split(',').map((country) => country.trim().toLowerCase()) ?? [];

      this.allLocations = Array.isArray(locations)
        ? locations.filter((location) => {
            const isInAllowedCountry =
              allowedCountries.length === 0 || allowedCountries.includes(location.country?.trim()?.toLowerCase());
            return isInAllowedCountry;
          })
        : [];

      // Initialize the map inside the specified container
      await this.initializeMap(this.element.querySelector('.map-container'));

      // Load all locations immediately for home page
      this.loadDefaultUSALocations();
      
      // Manually trigger the initial map update
      if (this.map && this.map.getBounds()) {
        this.handleMapChangeEvent(this.map.getBounds());
      }

    } catch (error) {
      console.error('Error initializing map location:', error);
    } finally {
      this.mapLoader.hideLoader();
      this.setFilterLoading(false);
    }
  }

  /**
   * Bind UI events for home page (simplified)
   */
  bindUIEvents() {
    // Home page typically has minimal UI interactions
    // Only bind essential events like map interactions
    
    // Bind search input event if enabled (simplified version)
    if (this.element.dataset.enableSearchInput === 'true') {
      this.bindSimpleSearchEvent();
    }

    // Bind near me event if enabled (simplified version)
    if (this.element.dataset.enableNearMe === 'true') {
      this.nearMeButtonElem = this.element.querySelector('.near-me-button');
      if (this.nearMeButtonElem) {
        this.bindSimpleNearMeEvent();
      }
    }
  }

  /**
   * Simplified search event for home page
   */
  bindSimpleSearchEvent() {
    const searchInput = this.element.querySelector('.search-input');
    if (searchInput) {
      searchInput.addEventListener('input', (e) => {
        const searchTerm = e.target.value.trim();
        if (searchTerm.length > 2) {
          this.performSimpleSearch(searchTerm);
        } else if (searchTerm.length === 0) {
          this.clearSearch();
        }
      });
    }
  }

  /**
   * Simplified near me event for home page
   */
  bindSimpleNearMeEvent() {
    this.nearMeButtonElem.addEventListener('click', () => {
      if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
          (position) => {
            this.centerMapOnLocation(position.coords.latitude, position.coords.longitude);
          },
          (error) => {
            console.warn('Geolocation error:', error);
          }
        );
      }
    });
  }

  /**
   * Determine if delayed filtering should be used (home page never uses delayed filtering)
   */
  shouldUseDelayedFiltering() {
    return false;
  }

  /**
   * Check if there are active filters (home page has minimal filters)
   */
  hasActiveFilters() {
    return false;
  }

  /**
   * Determine if sidebar should be displayed (home page typically doesn't show sidebar)
   */
  shouldDisplaySidebar() {
    return false;
  }

  /**
   * Handles the map idle event for home page (simplified)
   */
  handleMapChangeEvent(bounds, forceUpdate = false) {
    const filteredLocationsWithinBounds = this.filterLocationsWithinBounds(bounds);

    // Update the selected location coordinates
    this.selectedLocationCoordinates = {
      lat: this.map.getCenter().lat(),
      lng: this.map.getCenter().lng(),
    };

    // For home page, show all locations within bounds without complex filtering
    const newFilteredLocations = this.filterLocations(
      filteredLocationsWithinBounds,
      this.selectedLocationCoordinates.lat,
      this.selectedLocationCoordinates.lng,
      Infinity, // No radius filtering on home page
      null // No business type filtering on home page
    );

    // Only load locations if they have actually changed
    if (this.locationsHaveChanged(newFilteredLocations)) {
      this.previousFilteredLocations = [...newFilteredLocations];
      this.filteredLocations = newFilteredLocations;
      this.loadLocations(this.filteredLocations);
    }
  }

  // Filters locations within the current map bounds (simplified for home page)
  filterLocationsWithinBounds(bounds) {
    if (!bounds) return [];

    const filtered = this.allLocations.filter((location) => {
      const locationLatLng = new google.maps.LatLng(parseFloat(location.latitude), parseFloat(location.longitude));
      return bounds.contains(locationLatLng);
    });

    return filtered;
  }

  /**
   * Perform simple search for home page
   */
  performSimpleSearch(searchTerm) {
    const filteredLocations = this.allLocations.filter(location => 
      location.companyName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      location.city?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      location.state?.toLowerCase().includes(searchTerm.toLowerCase())
    );

    if (filteredLocations.length > 0) {
      // Center map on first result
      const firstResult = filteredLocations[0];
      this.centerMapOnLocation(parseFloat(firstResult.latitude), parseFloat(firstResult.longitude));
    }
  }

  /**
   * Clear search results
   */
  clearSearch() {
    // Reset to default view
    this.loadDefaultUSALocations();
  }

  /**
   * Center map on specific location
   */
  centerMapOnLocation(lat, lng) {
    this.selectedLocationCoordinates = { lat, lng };
    this.centerMapUpdateZoomLevel({ lat, lng }, this.markerCenterMapZoomLevel);
  }

  /**
   * Load default USA locations for home page
   */
  loadDefaultUSALocations() {
    this.selectedLocationCoordinates = this.defaultLocationCoordinates;
    this.isCenterChangeRestricted = false;
    this.selectedNearByMe = null;
    this.selectedRadius = Infinity; // No radius restriction on home page

    // Center map on default USA coordinates
    if (this.map) {
      this.centerMapUpdateZoomLevel(this.defaultLocationCoordinates, this.zoomLevel);
    }
  }

  /**
   * Check if locations have changed (simplified for home page)
   */
  locationsHaveChanged(newLocations) {
    if (!this.previousFilteredLocations) return true;
    if (this.previousFilteredLocations.length !== newLocations.length) return true;
    
    // Simple comparison by location IDs
    const previousIds = new Set(this.previousFilteredLocations.map(loc => loc.userId));
    const newIds = new Set(newLocations.map(loc => loc.userId));
    
    if (previousIds.size !== newIds.size) return true;
    
    for (let id of newIds) {
      if (!previousIds.has(id)) return true;
    }
    
    return false;
  }

  /**
   * Filter locations (simplified for home page)
   */
  filterLocations(locations, lat, lng, radius, businessType) {
    // For home page, just sort by distance without radius filtering
    return locations.sort((a, b) => {
      const distanceToA = this.calculateDistance(
        lat, lng,
        parseFloat(a.latitude), parseFloat(a.longitude)
      );
      const distanceToB = this.calculateDistance(
        lat, lng,
        parseFloat(b.latitude), parseFloat(b.longitude)
      );
      return distanceToA - distanceToB;
    });
  }

  /**
   * Load locations (simplified for home page)
   */
  loadLocations(locations) {
    // For home page, only show markers on map, no sidebar
    const markers = this.placeMarkers(locations);
    this.initMarkerClusterer(markers);
  }
}
