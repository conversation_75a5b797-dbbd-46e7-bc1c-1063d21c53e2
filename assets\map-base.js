import { GoogleMapsLoader } from '@theme/locations-services';
import { LocationApiService } from '@theme/locations-services';
import { MapLoader } from '@theme/locations-services';

/**
 * Base class for map functionality that can be extended by specific page implementations
 */
export class MapBase {
  constructor(element) {
    this.element = element;
    this.initializeProperties();
    this.mapLoader = new MapLoader(this.element.querySelector('.map-box-section'));
  }

  initializeProperties() {
    this.specialOfferingHealthPass = ['Preventative Body Scan', 'Healthpass', 'Health Screen'];
    this.isMobile = window.matchMedia('(max-width: 768px)').matches;
    this.geolocationErrorMessage = window.utilsString.locationsString.errorMessage;
    this.locationString = window.utilsString.locationsString;

    // Convert dataset value to a proper boolean
    this.isEnableRadiusFilter = this.element.dataset.enableRadiusFilter === 'true';
    this.isEnabledQueryParams = this.element.dataset.enableUpdateQuearyParams === 'true';

    // Default values for locations
    this.allLocations = [];
    this.mapMarkers = [];
    this.filteredLocations = [];
    this.markerClusterer = null;
    this.mapBound = null;
    this.infoWindow;
    this.map;
    this.defaultRadius = this.isEnableRadiusFilter ? Number(this.element.dataset.defaultRadius) : Infinity;
    this.defaultBusinessType = null;

    // Set default location coordinates
    this.defaultLocationCoordinates = {
      lat: parseFloat(this.element.dataset.defaultLatitude) || 34.052235,
      lng: parseFloat(this.element.dataset.defaultLongitude) || -118.243683,
    };

    this.selectedNearByMe = null;
    this.selectedInputSearch = null;
    this.selectedLocationCoordinates = this.defaultLocationCoordinates;
    this.selectedRadius = this.defaultRadius;
    this.selectedBusinessType = this.defaultBusinessType;
    this.googleMapId = this.element.dataset.googleMapId;

    this.zoomLevel = this.isMobile
      ? Number(this.element.dataset.defaultZoomMobile)
      : Number(this.element.dataset.defaultZoom) || 10;
    this.maxZoomLevel = this.isMobile
      ? Number(this.element.dataset.defaultMaxZoomMobile)
      : Number(this.element.dataset.defaultMaxZoom) || 10;

    this.markerCenterMapZoomLevel = this.isMobile
      ? Number(this.element.dataset.markerCenterMapZoomLevel)
      : Number(this.element.dataset.markerCenterMapZoomLevelMobile) || 10;
  }

  /**
   * Initialize the map location - to be implemented by subclasses
   */
  async initializeMapLocation() {
    throw new Error('initializeMapLocation must be implemented by subclass');
  }

  /**
   * Bind UI events - to be implemented by subclasses
   */
  bindUIEvents() {
    throw new Error('bindUIEvents must be implemented by subclass');
  }

  /**
   * Determine if delayed filtering should be used - to be implemented by subclasses
   */
  shouldUseDelayedFiltering() {
    throw new Error('shouldUseDelayedFiltering must be implemented by subclass');
  }

  /**
   * Check if there are active filters - to be implemented by subclasses
   */
  hasActiveFilters() {
    throw new Error('hasActiveFilters must be implemented by subclass');
  }

  /**
   * Determine if sidebar should be displayed - to be implemented by subclasses
   */
  shouldDisplaySidebar() {
    throw new Error('shouldDisplaySidebar must be implemented by subclass');
  }

  // Common utility methods that can be shared across implementations
  setFilterLoading(isLoading) {
    const filterWrapper = this.element.querySelector('.filter-wrapper');
    if (filterWrapper) {
      filterWrapper.dataset.loading = isLoading.toString();
    }
  }

  /**
   * Cleanup method to remove event listeners and prevent memory leaks
   */
  disconnectedCallback() {
    // Remove the idle event listener if it exists
    if (this.idleListener) {
      google.maps.event.removeListener(this.idleListener);
      this.idleListener = null;
    }

    // Remove the zoom change event listener if it exists
    if (this.zoomListener) {
      google.maps.event.removeListener(this.zoomListener);
      this.zoomListener = null;
    }

    // Clear any existing timeouts
    if (this.debouncedIdleHandler) {
      this.debouncedIdleHandler = null;
    }
  }

  /**
   * Debounce function to limit the frequency of function calls
   */
  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }

  /**
   * Initializes the Google Map instance within the given container.
   */
  async initializeMap(container) {
    this.mapOptions = {
      center: this.defaultLocationCoordinates,
      zoom: this.zoomLevel,
      zoomControl: true,
      draggable: true,
      mapTypeControl: false,
      streetViewControl: false,
      fullscreenControl: false,
      minZoom: this.zoomLevel,
      maxZoom: this.maxZoomLevel,
      mapId: this.googleMapId,
      disableDefaultUI: true,
    };

    this.map = new google.maps.Map(container, this.mapOptions);

    // Create a debounced version of the idle handler
    const ON_CHANGE_DEBOUNCE_TIMER = 300;
    this.debouncedIdleHandler = this.debounce(() => {
      this.mapBound = this.map.getBounds();
      if (this.mapBound) this.handleMapChangeEvent(this.mapBound);
    }, ON_CHANGE_DEBOUNCE_TIMER);

    this.idleListener = this.map.addListener('idle', this.debouncedIdleHandler);

    // Add zoom change listener to handle marker selection when clustering occurs
    this.zoomListener = this.map.addListener('zoom_changed', () => {
      // Use a small delay to allow clustering to complete before checking
      setTimeout(() => this.handleZoomChange(), 100);
    });
  }

  /**
   * Handles zoom change events to manage marker selection state during clustering.
   */
  handleZoomChange() {
    // Find the currently selected marker
    const selectedMarker = this.mapMarkers.find((marker) => marker.isSelected);

    if (!selectedMarker || !this.markerClusterer) {
      return; // No selected marker or clusterer not initialized
    }

    // Check if the selected marker is currently clustered
    const isMarkerClustered = this.isMarkerClustered(selectedMarker);

    if (isMarkerClustered) {
      // The selected marker is now part of a cluster, so deselect it
      this.deselectLocation();
    }
  }

  /**
   * Checks if a specific marker is currently part of a cluster.
   */
  isMarkerClustered(marker) {
    if (!this.markerClusterer || !marker) {
      return false;
    }

    try {
      // Check current zoom level against clustering threshold
      const currentZoom = this.map.getZoom();
      const clusteringMaxZoom = 13; // Should match the clustering configuration

      // If we're at or below clustering zoom and there are multiple markers nearby,
      // the marker is likely clustered
      if (currentZoom <= clusteringMaxZoom) {
        // Count nearby markers within a small radius
        const nearbyMarkers = this.mapMarkers.filter((otherMarker) => {
          if (otherMarker === marker) return false;

          const distance = this.calculateDistance(
            marker.position.lat,
            marker.position.lng,
            otherMarker.position.lat,
            otherMarker.position.lng
          );

          // If there are markers within ~100 meters, likely clustered
          return distance < 0.001; // Roughly 100 meters in degrees
        });

        // If there are nearby markers at clustering zoom level, marker is likely clustered
        return nearbyMarkers.length > 0;
      }

      return false; // At high zoom, markers are individual
    } catch (error) {
      // If there's an error, assume marker is not clustered
      console.warn('Error checking marker cluster status:', error);
      return false;
    }
  }

  /**
   * Calculates the distance between two geographic points.
   */
  calculateDistance(lat1, lng1, lat2, lng2) {
    const dLat = lat2 - lat1;
    const dLng = lng2 - lng1;
    return Math.sqrt(dLat * dLat + dLng * dLng);
  }

  /**
   * Centers the map on a given position and updates the zoom level.
   */
  centerMapUpdateZoomLevel(position, zoomLevel) {
    if (!this.map) return;
    this.map.setCenter(position);
    this.map.setZoom(zoomLevel);
  }

  /**
   * Deselects the currently selected location
   */
  deselectLocation() {
    // Close info window if it exists
    if (this.infoWindow) {
      this.infoWindow.close();
      this.infoWindow = null;
    }

    // Reset all sidebar items
    this.element.querySelectorAll('.sidebar-list').forEach((item) => {
      item.classList.remove('selected', 'selected-secondary');
      item.querySelector('.others-info-wrapper')?.classList.add('hidden');
    });

    // Reset marker selection state
    this.mapMarkers.forEach((marker) => {
      marker.isSelected = false;
      marker.zIndex = 0;
      marker.content.classList.remove('selected-marker');
    });
  }

  /**
   * Creates a marker for a given location
   */
  createMarker(map, location) {
    const mapMarker = document.createElement('div');
    mapMarker.className = 'marker-icon';
    mapMarker.innerHTML = `
      <svg width="24" height="32" viewBox="0 0 24 32" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M12 0C5.373 0 0 5.373 0 12c0 9 12 20 12 20s12-11 12-20c0-6.627-5.373-12-12-12z" fill="#D22725"/>
        <circle cx="12" cy="12" r="4" fill="white"/>
      </svg>
    `;

    const marker = new google.maps.marker.AdvancedMarkerElement({
      position: { lat: parseFloat(location.latitude), lng: parseFloat(location.longitude) },
      map: map,
      zIndex: location.isHealthPassPartner ? 1 : 0,
      title: location.companyName,
      content: mapMarker,
    });

    // Add isSelected property to track selected state
    marker.isSelected = false;
    return marker;
  }

  /**
   * Displays the provided location markers on the map.
   */
  placeMarkers(locations) {
    // Skip update if markers are already correct
    if (!this.markersNeedUpdate(locations)) {
      return this.mapMarkers;
    }

    // Get current map bounds to optimize marker visibility
    const bounds = this.map ? this.map.getBounds() : null;

    // Create a set of location IDs for quick lookup
    const newLocationIds = new Set(locations.map((loc) => loc.userId));

    // Remove markers that are no longer needed or outside bounds
    this.mapMarkers = this.mapMarkers.filter((marker) => {
      const shouldKeep = marker.locationId && newLocationIds.has(marker.locationId);

      // Also check if marker is within bounds (optional optimization)
      const withinBounds = !bounds || bounds.contains(marker.position);

      if (!shouldKeep || !withinBounds) {
        marker.setMap(null);
        return false;
      }
      return true;
    });

    // Get existing marker location IDs
    const existingLocationIds = new Set(this.mapMarkers.map((marker) => marker.locationId));

    // Add new markers for locations that don't already have markers
    locations.forEach((location) => {
      if (!existingLocationIds.has(location.userId)) {
        const marker = this.createMarker(this.map, location);
        marker.locationId = location.userId; // Store location ID for tracking
        this.mapMarkers.push(marker);

        // Add a click listener to handle marker interactions
        marker.addListener('gmp-click', () => {
          this.handleMarkerClick(marker, location);
        });
      }
    });

    return this.mapMarkers;
  }

  /**
   * Initializes or updates a MarkerClusterer with custom rendering and clustering logic.
   */
  initMarkerClusterer(markers) {
    // If clusterer already exists, update it with new markers instead of recreating
    if (this.markerClusterer) {
      this.markerClusterer.clearMarkers();
      this.markerClusterer.addMarkers(markers);
    } else {
      // Create new clusterer
      this.markerClusterer = new markerClusterer.MarkerClusterer({
        markers: markers,
        map: this.map,
        algorithm: new markerClusterer.GridAlgorithm({ gridSize: 38, maxZoom: 13 }),
        renderer: {
          render: ({ count, position, markers, map }) => {
            const clusterElement = document.createElement('div');
            clusterElement.className = 'custom-cluster';
            clusterElement.innerHTML = `<div class="cluster-content"><span class="cluster-count">${count}</span></div>`;
            clusterElement.addEventListener('click', () => {
              const bounds = new google.maps.LatLngBounds();
              markers.forEach((marker) => bounds.extend(marker.position));
              map.fitBounds(bounds, { padding: { top: 18, right: 18, bottom: 18, left: 18 } });
            });
            return new google.maps.marker.AdvancedMarkerElement({
              position,
              content: clusterElement,
            });
          },
        },
      });
    }
  }

  /**
   * Checks if the current markers match the provided locations.
   */
  markersNeedUpdate(locations) {
    // If different number of locations, definitely need update
    if (this.mapMarkers.length !== locations.length) {
      return true;
    }

    // Check if all location IDs match
    const currentLocationIds = new Set(this.mapMarkers.map((marker) => marker.locationId));
    const newLocationIds = new Set(locations.map((loc) => loc.userId));

    // If sets are different sizes or have different values, need update
    if (currentLocationIds.size !== newLocationIds.size) {
      return true;
    }

    for (let id of newLocationIds) {
      if (!currentLocationIds.has(id)) {
        return true;
      }
    }

    return false;
  }

  /**
   * Handle marker click event (basic implementation)
   */
  handleMarkerClick(marker, location) {
    // Basic implementation - can be overridden by subclasses
    console.log('Marker clicked:', location.companyName, 'Marker:', marker.title);
  }
}
