# Map Location Refactor Documentation

## Overview

The location map functionality has been refactored into a modular, reusable architecture that separates concerns between different page types (home page vs. location page) while maintaining shared functionality in a base class.

## Architecture

### File Structure

```
assets/
├── map-base.js           # Base class with shared functionality
├── location-page.js      # Location page specific implementation
├── home-page-map.js      # Home page specific implementation
├── map-factory.js        # Factory for creating appropriate map instances
├── locations-services.js # Existing services (unchanged)
└── location.js           # Original file (can be removed after migration)
```

### Class Hierarchy

```
MapBase (abstract)
├── LocationPage (full-featured implementation)
└── HomePageMap (simplified implementation)
```

## Key Features

### 1. **Automatic Page Detection**
The `MapFactory` automatically detects the current page type using multiple methods:
- Shopify template variables
- Meta tags
- URL path analysis
- DOM element detection
- Body classes

### 2. **Modular Design**
- **MapBase**: Contains shared functionality like map initialization, marker management, clustering
- **LocationPage**: Full-featured implementation with filters, sidebar, URL params
- **HomePageMap**: Simplified implementation for home page use

### 3. **Import Map Support**
Uses modern ES6 import maps for clean module resolution:
```html
<script type="importmap">
{
  "imports": {
    "@theme/map-base": "/assets/map-base.js",
    "@theme/location-page": "/assets/location-page.js",
    "@theme/home-page-map": "/assets/home-page-map.js",
    "@theme/map-factory": "/assets/map-factory.js"
  }
}
</script>
```

## Usage

### Basic Implementation

The refactored code maintains the same HTML structure:

```html
<map-location 
  data-google-api-key="your-key"
  data-locations-api="your-api-url"
  data-enable-sidebar="true"
  data-enable-radius-filter="true">
  <div class="map-container"></div>
  <div class="sidebar-location"></div>
</map-location>
```

### Page-Specific Behavior

#### Location Page Features:
- Full filtering capabilities (radius, business type, search)
- Sidebar with location list
- URL parameter management
- Mobile "Apply Now" button support
- Delayed filtering on mobile

#### Home Page Features:
- Simplified map display
- Basic search functionality
- No sidebar
- Immediate filtering
- Optimized for overview display

## Configuration

### Data Attributes

The map behavior is controlled through data attributes on the `<map-location>` element:

```html
<!-- Location Page Configuration -->
<map-location 
  data-enable-sidebar="true"
  data-enable-radius-filter="true"
  data-enable-business-type-filter="true"
  data-enable-search-input="true"
  data-enable-near-me="true"
  data-enable-update-queary-params="true"
  data-default-radius="25"
  data-default-zoom="10"
  data-default-zoom-mobile="8">
</map-location>

<!-- Home Page Configuration -->
<map-location 
  data-enable-sidebar="false"
  data-enable-radius-filter="false"
  data-enable-search-input="true"
  data-enable-near-me="true"
  data-default-zoom="5">
</map-location>
```

## Migration Guide

### From Original location.js

1. **Update HTML includes**: Replace the old script tags with the new import map structure
2. **No HTML changes needed**: The `<map-location>` element works the same way
3. **Configuration**: Ensure proper data attributes are set for each page type

### Template Updates

Update `snippets/scripts-and-styles.liquid`:

```liquid
<script type="importmap">
{
  "imports": {
    "@theme/locations-services": "{{ 'locations-services.js' | asset_url }}",
    "@theme/map-base": "{{ 'map-base.js' | asset_url }}",
    "@theme/location-page": "{{ 'location-page.js' | asset_url }}",
    "@theme/home-page-map": "{{ 'home-page-map.js' | asset_url }}",
    "@theme/map-factory": "{{ 'map-factory.js' | asset_url }}"
  }
}
</script>

{% if template == 'index' or template == 'page.locations' %}
  <script src="{{ 'map-factory.js' | asset_url }}" type="module" defer></script>
{% endif %}
```

## Benefits

### 1. **Maintainability**
- Clear separation of concerns
- Easier to modify page-specific behavior
- Reduced code duplication

### 2. **Performance**
- Only loads necessary functionality per page
- Optimized clustering and marker management
- Efficient memory usage

### 3. **Extensibility**
- Easy to add new page types
- Modular architecture supports new features
- Clean inheritance structure

### 4. **Developer Experience**
- Modern ES6 modules
- Clear API boundaries
- Comprehensive error handling

## Advanced Features

### Custom Page Types

To add a new page type:

1. Create a new class extending `MapBase`
2. Implement required abstract methods
3. Add detection logic to `MapFactory.detectPageType()`
4. Add configuration to `MapFactory.getPageConfig()`

### Marker Clustering

The refactored code includes improved marker clustering with:
- Automatic deselection when markers become clustered
- Optimized cluster detection
- Configurable clustering thresholds

### Zoom-based Selection Management

- Markers are automatically deselected when they become part of clusters
- Smart detection of clustering state
- Preserves user selection when appropriate

## Browser Support

- Modern browsers with ES6 module support
- Import maps support (Chrome 89+, Firefox 108+, Safari 16.4+)
- Graceful fallback for older browsers

## Troubleshooting

### Common Issues

1. **Import map not supported**: Ensure browser supports import maps or add a polyfill
2. **Module loading errors**: Check that all file paths in import map are correct
3. **Page detection fails**: Verify template variables or add explicit `data-page-type` attribute

### Debug Mode

Enable debug logging by setting:
```javascript
window.MAP_DEBUG = true;
```

This will log page detection results and configuration details to the console.
